import { Post } from '../../entities/Post';
import { Page } from '../../shared/services/app-base/page-base';
import { HomeServices } from './services';
import { render } from 'solid-js/web';
import PostsContainer from './components/posts-container';
import { HealthContentAPI } from '../../shared/infra';

window.addEventListener('load', async () => {
  new Home();
});
export class Home extends Page {
  readonly services;

  readonly api;

  constructor() {
    super();
    this.api = new HealthContentAPI();
    this.services = new HomeServices();
    this.renderComponents();
  }

  public async getPost(): Promise<Post[]> {
    try {
      const data = await this.api.getHealthPostByStatus();

      return data;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al obtener las publicaciones' });

      return [];
    }
  }

  public async updatePost(post: Post, content: string): Promise<Post | null> {
    try {
      this.showLoadingScreen();
      const data = await this.api.updatePost({
        ...post,
        content,
      });

      return data;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al actualizar contenido' });

      return null;
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async postLike(post: Post): Promise<void> {
    try {
      await this.api.updateLikeCount(post);
    } catch (error) {
      this.showErrorAlert({ message: 'Error al registrar like' });
    } finally {
      this.hideLoadingScreen();
    }
  }

  private async renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer =
        this.services.getHTMLElement('posts-container');
      render(() => <PostsContainer context={this} />, userProfileContainer);
    }
  }
}
